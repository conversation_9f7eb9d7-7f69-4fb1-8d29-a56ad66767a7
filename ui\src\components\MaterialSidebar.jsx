import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
  Chip,
  Paper
} from '@mui/material';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import PlayCircleIcon from '@mui/icons-material/PlayCircle';
import QueueMusicIcon from '@mui/icons-material/QueueMusic';
import LibraryMusicIcon from '@mui/icons-material/LibraryMusic';
import ScheduleIcon from '@mui/icons-material/Schedule';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import AssessmentIcon from '@mui/icons-material/Assessment';
import RadioIcon from '@mui/icons-material/Radio';
import StoreIcon from '@mui/icons-material/Store';
import UploadIcon from '@mui/icons-material/Upload';
import MonitorIcon from '@mui/icons-material/Monitor';
import HeadphonesIcon from '@mui/icons-material/Headphones';
import EditIcon from '@mui/icons-material/Edit';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import EventIcon from '@mui/icons-material/Event';
import MessageIcon from '@mui/icons-material/Message';
import SettingsIcon from '@mui/icons-material/Settings';
import HelpIcon from '@mui/icons-material/Help';
import LogoutIcon from '@mui/icons-material/Logout';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import GavelIcon from '@mui/icons-material/Gavel';
import WarningIcon from '@mui/icons-material/Warning';
import PaymentIcon from '@mui/icons-material/Payment';
import VerifiedIcon from '@mui/icons-material/Verified';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { useAuth } from '../context/AuthContext';
import { storeService } from '../services/api';

const DRAWER_WIDTH = 256;

const MaterialSidebar = ({ isOpen, setIsOpen }) => {
  const { user, logout, isAdmin, isCompliance, isSAMRO, isSAMPRA } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const [store, setStore] = useState(null);
  const [loadingStore, setLoadingStore] = useState(false);

  // Get current tab from URL
  const searchParams = new URLSearchParams(location.search);
  const currentTab = searchParams.get('tab') || (isAdmin ? 'analytics' : isCompliance ? 'overview' : 'player');

  // Fetch store information when user changes and is not admin
  useEffect(() => {
    const fetchStoreInfo = async () => {
      if (!isAdmin && user?.storeId) {
        // Check if user already has store information
        if (user.store?.name) {
          setStore(user.store);
          return;
        }

        // Otherwise fetch from API
        if (!store) {
          setLoadingStore(true);
          try {
            const response = await storeService.getById(user.storeId);
            setStore(response.data);
          } catch (error) {
            console.error('Failed to fetch store information:', error);
            // Fallback to a default name if fetch fails
            setStore({ name: 'Store' });
          } finally {
            setLoadingStore(false);
          }
        }
      } else {
        // Reset store state if user is admin or no user
        setStore(null);
      }
    };

    fetchStoreInfo();
  }, [user, isAdmin]);

  const storeMenuItems = [
    { id: 'player', name: 'Music Player', icon: PlayCircleIcon, path: '/store?tab=player' },
    { id: 'playlists', name: 'Playlists', icon: QueueMusicIcon, path: '/store?tab=playlists' },
    { id: 'schedule', name: 'Schedule', icon: ScheduleIcon, path: '/store?tab=schedule' },
    { id: 'analytics', name: 'Analytics', icon: AnalyticsIcon, path: '/store?tab=analytics' },
    { id: 'licenses', name: 'My Licenses', icon: GavelIcon, path: '/store?tab=licenses' },
    { id: 'radio', name: 'Radio Stations', icon: HeadphonesIcon, path: '/store?tab=radio' },
    { id: 'messages', name: 'Messages', icon: MessageIcon, path: '/store?tab=messages' },
    { id: 'account', name: 'Account', icon: AccountCircleIcon, path: '/store?tab=account' },
    { id: 'help', name: 'Help', icon: HelpIcon, path: '/store?tab=help' },
  ];

  const adminMenuItems = [
    { id: 'analytics', name: 'Dashboard', icon: AnalyticsIcon, path: '/admin?tab=analytics' },
    { id: 'tracks', name: 'Track Library', icon: MusicNoteIcon, path: '/admin?tab=tracks' },
    // { id: 'uploads', name: 'Uploads', icon: UploadIcon, path: '/admin?tab=uploads' },
    { id: 'playlists', name: 'Playlists', icon: HeadphonesIcon, path: '/admin?tab=playlists' },
    { id: 'radio', name: 'Radio Stations', icon: RadioIcon, path: '/admin?tab=radio' },
    { id: 'trending', name: 'Trending', icon: TrendingUpIcon, path: '/admin?tab=trending' },
    { id: 'reports', name: 'Reports', icon: AssessmentIcon, path: '/admin?tab=reports' },
    { id: 'stores', name: 'Store Management', icon: StoreIcon, path: '/admin?tab=stores' },
    { id: 'compliance', name: 'Activity Monitor', icon: GavelIcon, path: '/admin?tab=compliance' },
    { id: 'licenses', name: 'License Management', icon: GavelIcon, path: '/admin?tab=licenses' },
    { id: 'messages', name: 'Messages', icon: MessageIcon, path: '/admin?tab=messages' },
    { id: 'monitoring', name: 'Monitoring', icon: MonitorIcon, path: '/admin?tab=monitoring' },
    { id: 'account', name: 'Account', icon: AccountCircleIcon, path: '/admin?tab=account' },
    { id: 'settings', name: 'Settings', icon: SettingsIcon, path: '/admin?tab=settings' },
  ];

  // Base compliance menu items for all compliance users
  const baseComplianceMenuItems = [
    { id: 'overview', name: 'Overview', icon: AnalyticsIcon, path: '/compliance?tab=overview' },
    { id: 'reports', name: 'Reports', icon: AssessmentIcon, path: '/compliance?tab=reports' },
    { id: 'analytics', name: 'Analytics', icon: TrendingUpIcon, path: '/compliance?tab=analytics' },
    { id: 'collection', name: 'Data Collection', icon: MonitorIcon, path: '/compliance?tab=collection' },
    { id: 'licenses', name: 'License Management', icon: GavelIcon, path: '/compliance?tab=licenses' },
    { id: 'messages', name: 'Messages', icon: MessageIcon, path: '/compliance?tab=messages' },
    { id: 'account', name: 'Account', icon: AccountCircleIcon, path: '/compliance?tab=account' },
  ];

  // Audit trail menu item - only for compliance officers and admins, not SAMRO/SAMPRA staff
  const auditTrailMenuItem = { id: 'audit', name: 'Audit Trail', icon: SettingsIcon, path: '/compliance?tab=audit' };

  // Build compliance menu items based on user role
  const complianceMenuItems = [...baseComplianceMenuItems];

  // Only add audit trail for compliance_admin role, not for samro_staff or sampra_staff
  if (user?.role === 'compliance_admin') {
    // Insert audit trail before messages
    complianceMenuItems.splice(-2, 0, auditTrailMenuItem);
  }

  const menuItems = isAdmin ? adminMenuItems : isCompliance ? complianceMenuItems : storeMenuItems;

  const handleMenuClick = (item) => {
    navigate(item.path);
    if (isMobile) {
      setIsOpen(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Box
              sx={{
                width: 32,
                height: 32,
                bgcolor: isAdmin ? 'primary.main' : 'success.main',
                borderRadius: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <MusicNoteIcon sx={{ color: 'white', fontSize: 20 }} />
            </Box>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                {isAdmin ? 'Dashboard' : isCompliance ? (isSAMRO ? 'SAMRO' : isSAMPRA ? 'SAMPRA' : 'Compliance') : 'TrakSong'}
              </Typography>
              {!isAdmin && !isCompliance && (
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  {loadingStore ? 'Loading...' : (store?.name || 'Store')}
                </Typography>
              )}
              {isCompliance && (
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  {user?.organization || 'Compliance Dashboard'}
                </Typography>
              )}
            </Box>
          </Box>
          {isMobile && (
            <IconButton onClick={() => setIsOpen(false)} size="small">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, p: 2 }}>
        <List sx={{ p: 0 }}>
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentTab === item.id;

            return (
              <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
                <ListItemButton
                  onClick={() => handleMenuClick(item)}
                  sx={{
                    borderRadius: 1.5,
                    py: 1.5,
                    px: 2,
                    bgcolor: isActive ? (isAdmin ? 'primary.main' : isCompliance ? (isSAMRO ? 'primary.main' : 'secondary.main') : 'success.main') : 'transparent',
                    color: isActive ? 'white' : 'text.secondary',
                    '&:hover': {
                      bgcolor: isActive
                        ? (isAdmin ? 'primary.dark' : isCompliance ? (isSAMRO ? 'primary.dark' : 'secondary.dark') : 'success.dark')
                        : 'action.hover'
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Icon sx={{
                      color: isActive ? 'white' : 'text.secondary',
                      fontSize: 20
                    }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={item.name}
                    primaryTypographyProps={{
                      fontSize: 14,
                      fontWeight: isActive ? 600 : 500
                    }}
                  />
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>

        {/* Store Status (for store dashboard) */}
        {!isAdmin && !isCompliance && (
          <Paper
            sx={{
              mt: 3,
              p: 2,
              bgcolor: 'rgba(255,255,255,0.05)',
              border: '1px solid rgba(255,255,255,0.1)'
            }}
          >
            <Typography variant="caption" sx={{ color: 'text.secondary', textTransform: 'uppercase', letterSpacing: 1 }}>
              Status
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  bgcolor: 'success.main',
                  borderRadius: '50%',
                  animation: 'pulse 2s infinite'
                }}
              />
              <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 500 }}>
                Online
              </Typography>
            </Box>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              Last sync: 2 min ago
            </Typography>
          </Paper>
        )}
      </Box>

      {/* User Section */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 2 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'grey.600' }}>
            <PersonIcon sx={{ fontSize: 16 }} />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
              {isAdmin ? 'Admin User' : isCompliance ? (isSAMRO ? 'SAMRO Staff' : isSAMPRA ? 'SAMPRA Staff' : 'Compliance Officer') : 'Store Manager'}
            </Typography>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {isAdmin ? 'Administrator' : isCompliance ? user?.organization : 'Store Manager'}
            </Typography>
          </Box>
        </Box>

        <ListItemButton
          onClick={handleLogout}
          sx={{
            borderRadius: 1.5,
            py: 1,
            px: 2,
            color: 'error.main',
            '&:hover': {
              bgcolor: 'error.main',
              color: 'white',
              '& .MuiListItemIcon-root': {
                color: 'white'
              }
            }
          }}
        >
          <ListItemIcon sx={{ minWidth: 36 }}>
            <LogoutIcon sx={{ fontSize: 16, color: 'error.main' }} />
          </ListItemIcon>
          <ListItemText
            primary="Logout"
            primaryTypographyProps={{ fontSize: 14 }}
          />
        </ListItemButton>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'permanent'}
      open={isOpen}
      onClose={() => setIsOpen(false)}
      sx={{
        width: DRAWER_WIDTH,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: DRAWER_WIDTH,
          boxSizing: 'border-box',
          bgcolor: '#1e293b',
          color: 'white',
          borderRight: '1px solid rgba(255,255,255,0.1)'
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default MaterialSidebar;
