import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Stack,
  Pagination,
  Alert,
  LinearProgress,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  MusicNote as MusicIcon,
  Radio as RadioIcon,
  SwapHoriz as SwitchIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { complianceService, storeService } from '../services/api';
import { format } from 'date-fns';

const MaterialComplianceActivityMonitor = () => {
  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState([]);
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState('all');
  const [activityType, setActivityType] = useState('all');
  const [sourceFilter, setSourceFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [summary, setSummary] = useState(null);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [complianceDialogOpen, setComplianceDialogOpen] = useState(false);
  const [complianceUpdating, setComplianceUpdating] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    console.log('MaterialComplianceActivityMonitor: Fetching data...');
    fetchStores();
    fetchActivities();
    fetchSummary();
  }, [selectedStore, activityType, sourceFilter, dateRange, page]);

  useEffect(() => {
    console.log('MaterialComplianceActivityMonitor: Component mounted');
    fetchStores();
  }, []);

  const fetchActivities = async () => {
    try {
      console.log('fetchActivities: Starting...');
      setLoading(true);
      setError(null);
      const params = {
        page,
        limit: 20,
        startDate: dateRange.start,
        endDate: dateRange.end
      };

      if (selectedStore !== 'all') {
        params.storeId = selectedStore;
      }
      if (activityType !== 'all') {
        params.activityType = activityType;
      }
      if (sourceFilter !== 'all') {
        if (sourceFilter === 'switching') {
          params.activityType = 'source_switch';
        } else {
          params.sourceFrom = sourceFilter;
        }
      }

      console.log('fetchActivities: Calling API with params:', params);
      const response = await complianceService.getAllActivities(params);
      console.log('fetchActivities: API response:', response);
      setActivities(response.data.data);
      setTotalPages(response.data.pagination.pages);
      console.log('fetchActivities: Success - set', response.data.data.length, 'activities');
    } catch (error) {
      console.error('fetchActivities: Error occurred:', error);
      console.error('fetchActivities: Error details:', error.response?.data || error.message);
      setError(`Failed to fetch activities: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
      console.log('fetchActivities: Completed');
    }
  };

  const fetchStores = async () => {
    try {
      console.log('fetchStores: Starting...');
      const response = await storeService.getAll();
      console.log('fetchStores: API response:', response);

      // Handle different response structures
      let storesData = [];
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        storesData = response.data.data;
      } else if (response.data && Array.isArray(response.data)) {
        storesData = response.data;
      } else if (Array.isArray(response)) {
        storesData = response;
      }

      console.log('fetchStores: Setting stores data:', storesData);
      setStores(storesData);
    } catch (error) {
      console.error('Error fetching stores:', error);
      setStores([]); // Ensure stores is always an array
    }
  };

  const fetchSummary = async () => {
    try {
      const params = {
        startDate: dateRange.start,
        endDate: dateRange.end
      };
      if (selectedStore !== 'all') {
        params.storeId = selectedStore;
      }

      console.log('Fetching summary with params:', params);
      const response = await complianceService.getActivitySummary(params);
      console.log('Summary response:', response);

      // Transform the backend response to match frontend expectations
      const backendData = response.data.data;
      const transformedSummary = {
        totalActivities: backendData.activityCounts.reduce((sum, item) => sum + item.count, 0),
        sourceSwitches: backendData.sourceSwitchingStats.reduce((sum, item) => sum + item.count, 0),
        pendingReports: backendData.complianceSummary.pending || 0,
        activeStores: new Set(activities.map(a => a.storeId)).size
      };

      console.log('Transformed summary:', transformedSummary);
      setSummary(transformedSummary);
    } catch (error) {
      console.error('Error fetching summary:', error);
      console.error('Summary error details:', error.response?.data || error.message);
    }
  };

  const handleMarkAsReported = async (activityIds, organization) => {
    try {
      await complianceService.markActivitiesAsReported(activityIds, organization);
      fetchActivities(); // Refresh data
    } catch (error) {
      console.error('Error marking as reported:', error);
    }
  };

  const handleViewDetails = (activity) => {
    setSelectedActivity(activity);
    setDetailDialogOpen(true);
  };

  const handleEditCompliance = (activity) => {
    setSelectedActivity(activity);
    setComplianceDialogOpen(true);
  };

  const handleUpdateCompliance = async (activityIds, organization) => {
    try {
      setComplianceUpdating(true);
      await complianceService.markActivitiesAsReported(activityIds, organization);

      // Refresh activities to show updated compliance status
      await fetchActivities();
      await fetchSummary();

      setComplianceDialogOpen(false);
      setSelectedActivity(null);
    } catch (error) {
      console.error('Error updating compliance status:', error);
    } finally {
      setComplianceUpdating(false);
    }
  };

  const getActivityIcon = (activityType) => {
    if (activityType.includes('music')) return <MusicIcon />;
    if (activityType.includes('radio')) return <RadioIcon />;
    if (activityType.includes('switch')) return <SwitchIcon />;
    return <MusicIcon />;
  };

  const getActivityColor = (activityType) => {
    if (activityType.includes('start')) return 'success';
    if (activityType.includes('stop')) return 'error';
    if (activityType.includes('switch')) return 'warning';
    return 'default';
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  return (
    <Box sx={{ p: 3 }}>


      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Store Activity Monitor
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchActivities}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      {summary && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  Total Activities
                </Typography>
                <Typography variant="h4">
                  {summary.totalActivities || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="success.main">
                  Source Switches
                </Typography>
                <Typography variant="h4">
                  {summary.sourceSwitches || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="warning.main">
                  Pending Reports
                </Typography>
                <Typography variant="h4">
                  {summary.pendingReports || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="info.main">
                  Active Stores
                </Typography>
                <Typography variant="h4">
                  {summary.activeStores || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Store</InputLabel>
              <Select
                value={selectedStore}
                label="Store"
                onChange={(e) => setSelectedStore(e.target.value)}
              >
                <MenuItem value="all">All Stores</MenuItem>
                {Array.isArray(stores) && stores.map((store) => (
                  <MenuItem key={store._id} value={store._id}>
                    {store.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Activity Type</InputLabel>
              <Select
                value={activityType}
                label="Activity Type"
                onChange={(e) => setActivityType(e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="source_switch">Source Switches</MenuItem>
                <MenuItem value="music_player_start">Music Start</MenuItem>
                <MenuItem value="music_player_stop">Music Stop</MenuItem>
                <MenuItem value="radio_start">Radio Start</MenuItem>
                <MenuItem value="radio_stop">Radio Stop</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Source</InputLabel>
              <Select
                value={sourceFilter}
                label="Source"
                onChange={(e) => setSourceFilter(e.target.value)}
              >
                <MenuItem value="all">All Sources</MenuItem>
                <MenuItem value="music_player">Music Player</MenuItem>
                <MenuItem value="radio_stream">Radio Stream</MenuItem>
                <MenuItem value="switching">Source Switching</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="Start Date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="End Date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              onClick={fetchActivities}
              disabled={loading}
            >
              Apply Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Activities Table */}
      <Paper sx={{ borderRadius: 3 }}>
        {loading && <LinearProgress />}
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Timestamp</TableCell>
                <TableCell>Store</TableCell>
                <TableCell>Activity</TableCell>
                <TableCell>Source Transition</TableCell>
                <TableCell>Duration</TableCell>
                <TableCell>Compliance Status</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {activities.map((activity) => (
                <TableRow key={activity._id}>
                  <TableCell>
                    <Typography variant="body2">
                      {format(new Date(activity.timestamp), 'MMM dd, yyyy HH:mm:ss')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {activity.storeId?.name || 'Unknown Store'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getActivityIcon(activity.activityType)}
                      <Chip
                        label={activity.activityType.replace('_', ' ').toUpperCase()}
                        color={getActivityColor(activity.activityType)}
                        size="small"
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    {activity.sourceFrom && activity.sourceTo && (
                      <Typography variant="body2">
                        {activity.sourceFrom} → {activity.sourceTo}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDuration(activity.duration)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Stack direction="row" spacing={1}>
                        {activity.compliance?.reportedToSAMRO && (
                          <Chip label="SAMRO" color="success" size="small" />
                        )}
                        {activity.compliance?.reportedToSAMPRA && (
                          <Chip label="SAMPRA" color="success" size="small" />
                        )}
                        {activity.compliance?.requiresReporting &&
                         !activity.compliance?.reportedToSAMRO &&
                         !activity.compliance?.reportedToSAMPRA && (
                          <Chip label="Pending" color="warning" size="small" />
                        )}
                        {!activity.compliance?.requiresReporting && (
                          <Chip label="Not Required" color="default" size="small" />
                        )}
                      </Stack>
                      {activity.compliance?.requiresReporting && (
                        <Tooltip title="Edit Compliance Status">
                          <IconButton
                            size="small"
                            onClick={() => handleEditCompliance(activity)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Stack>
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewDetails(activity)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={(_, newPage) => setPage(newPage)}
            color="primary"
          />
        </Box>
      </Paper>

      {/* Activity Detail Dialog */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Activity Details</DialogTitle>
        <DialogContent>
          {selectedActivity && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Activity Type
                  </Typography>
                  <Typography variant="body1">
                    {selectedActivity.activityType}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Timestamp
                  </Typography>
                  <Typography variant="body1">
                    {format(new Date(selectedActivity.timestamp), 'PPpp')}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Source Transition
                  </Typography>
                  <Typography variant="body1">
                    {selectedActivity.sourceFrom} → {selectedActivity.sourceTo}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Duration
                  </Typography>
                  <Typography variant="body1">
                    {formatDuration(selectedActivity.duration)}
                  </Typography>
                </Grid>
                {selectedActivity.musicPlayerData && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                        Music Player Data
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Track
                      </Typography>
                      <Typography variant="body1">
                        {selectedActivity.musicPlayerData.trackTitle}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Artist
                      </Typography>
                      <Typography variant="body1">
                        {selectedActivity.musicPlayerData.trackArtist}
                      </Typography>
                    </Grid>
                  </>
                )}
                {selectedActivity.radioData && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                        Radio Data
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Station
                      </Typography>
                      <Typography variant="body1">
                        {selectedActivity.radioData.stationName}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Genre
                      </Typography>
                      <Typography variant="body1">
                        {selectedActivity.radioData.stationGenre}
                      </Typography>
                    </Grid>
                  </>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Compliance Status Edit Dialog */}
      <Dialog
        open={complianceDialogOpen}
        onClose={() => setComplianceDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Update Compliance Status
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Mark this activity as reported to compliance organizations
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedActivity && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Activity: {selectedActivity.activityType?.replace('_', ' ').toUpperCase()}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Store: {selectedActivity.storeId?.name || 'Unknown Store'}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Date: {format(new Date(selectedActivity.timestamp), 'MMM dd, yyyy HH:mm:ss')}
              </Typography>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Current Status:
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mb: 3 }}>
                  {selectedActivity.compliance?.reportedToSAMRO && (
                    <Chip label="SAMRO Reported" color="success" size="small" />
                  )}
                  {selectedActivity.compliance?.reportedToSAMPRA && (
                    <Chip label="SAMPRA Reported" color="success" size="small" />
                  )}
                  {selectedActivity.compliance?.requiresReporting &&
                   !selectedActivity.compliance?.reportedToSAMRO &&
                   !selectedActivity.compliance?.reportedToSAMPRA && (
                    <Chip label="Pending Reporting" color="warning" size="small" />
                  )}
                </Stack>

                <Typography variant="subtitle1" gutterBottom>
                  Mark as Reported to:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Select which organization(s) this activity has been reported to for compliance tracking.
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setComplianceDialogOpen(false)}>
            Cancel
          </Button>
          {selectedActivity && !selectedActivity.compliance?.reportedToSAMRO && (
            <Button
              onClick={() => handleUpdateCompliance([selectedActivity._id], 'SAMRO')}
              disabled={complianceUpdating}
              variant="contained"
              color="primary"
            >
              {complianceUpdating ? 'Updating...' : 'Mark SAMRO Reported'}
            </Button>
          )}
          {selectedActivity && !selectedActivity.compliance?.reportedToSAMPRA && (
            <Button
              onClick={() => handleUpdateCompliance([selectedActivity._id], 'SAMPRA')}
              disabled={complianceUpdating}
              variant="contained"
              color="secondary"
            >
              {complianceUpdating ? 'Updating...' : 'Mark SAMPRA Reported'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialComplianceActivityMonitor;
