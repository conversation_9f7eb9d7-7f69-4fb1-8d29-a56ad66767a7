const mongoose = require('mongoose');
const Store = require('../models/Store.model');
const PlayHistory = require('../models/PlayHistory.model');
const Track = require('../models/Track.model');
const Playlist = require('../models/Playlist.model');

// Get all stores
exports.getAllStores = async (req, res) => {
  try {
    const stores = await Store.find({})
      .populate('users', 'username role')
      .select('name location status createdAt updatedAt');

    res.json({
      success: true,
      data: stores
    });
  } catch (error) {
    console.error('Error fetching stores:', error);
    res.status(500).json({
      error: 'Failed to fetch stores',
      details: error.message
    });
  }
};

// Get store by ID
exports.getStoreById = async (req, res) => {
  try {
    const { id } = req.params;
    const store = await Store.findById(id).populate('users', 'username role');

    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json({
      success: true,
      data: store
    });
  } catch (error) {
    console.error('Error fetching store:', error);
    res.status(500).json({
      error: 'Failed to fetch store',
      details: error.message
    });
  }
};

// Get store analytics
exports.getStoreAnalytics = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeRange = 'week' } = req.query;

    // Validate store exists
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    // Build time range filter
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    const matchConditions = {
      storeId: new mongoose.Types.ObjectId(storeId),
      playedDate: { $gte: startDate }
    };

    // Get basic analytics
    const [basicStats, hourlyStats, genreStats, playlistStats] = await Promise.all([
      // Basic statistics
      PlayHistory.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: null,
            totalPlays: { $sum: 1 },
            totalDuration: { $sum: '$durationPlayed' },
            uniqueTracks: { $addToSet: '$trackId' },
            avgCompletionRate: { $avg: '$completionPercentage' }
          }
        }
      ]),

      // Hourly play distribution
      PlayHistory.aggregate([
        { $match: matchConditions },
        {
          $group: {
            _id: { $hour: '$playedDate' },
            plays: { $sum: 1 },
            duration: { $sum: '$durationPlayed' }
          }
        },
        { $sort: { '_id': 1 } }
      ]),

      // Genre distribution
      PlayHistory.aggregate([
        { $match: matchConditions },
        {
          $lookup: {
            from: 'tracks',
            localField: 'trackId',
            foreignField: '_id',
            as: 'track'
          }
        },
        { $unwind: '$track' },
        {
          $group: {
            _id: '$track.genre',
            plays: { $sum: 1 },
            duration: { $sum: '$durationPlayed' }
          }
        },
        { $sort: { plays: -1 } }
      ]),

      // Playlist usage
      PlayHistory.aggregate([
        {
          $match: {
            ...matchConditions,
            playlistId: { $exists: true, $ne: null }
          }
        },
        {
          $lookup: {
            from: 'playlists',
            localField: 'playlistId',
            foreignField: '_id',
            as: 'playlist'
          }
        },
        { $unwind: '$playlist' },
        {
          $group: {
            _id: '$playlistId',
            name: { $first: '$playlist.name' },
            plays: { $sum: 1 },
            duration: { $sum: '$durationPlayed' }
          }
        },
        { $sort: { plays: -1 } }
      ])
    ]);

    const analytics = {
      basic: basicStats[0] || {
        totalPlays: 0,
        totalDuration: 0,
        uniqueTracks: [],
        avgCompletionRate: 0
      },
      hourlyDistribution: hourlyStats,
      genreDistribution: genreStats,
      playlistUsage: playlistStats,
      timeRange,
      store: {
        id: store._id,
        name: store.name,
        address: store.address,
        timezone: store.timezone
      }
    };

    // Process unique tracks count
    analytics.basic.uniqueTracks = analytics.basic.uniqueTracks.length;

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Error fetching store analytics:', error);
    res.status(500).json({
      error: 'Failed to fetch store analytics',
      details: error.message
    });
  }
};

// Get store top tracks
exports.getStoreTopTracks = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { limit = 10, timeRange = 'week' } = req.query;

    // Validate store exists
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    // Build time range filter
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    const matchConditions = {
      storeId: new mongoose.Types.ObjectId(storeId),
      playedDate: { $gte: startDate }
    };

    const topTracks = await PlayHistory.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: '$trackId',
          plays: { $sum: 1 },
          totalDuration: { $sum: '$durationPlayed' },
          avgCompletionRate: { $avg: '$completionPercentage' },
          lastPlayed: { $max: '$playedDate' }
        }
      },
      {
        $lookup: {
          from: 'tracks',
          localField: '_id',
          foreignField: '_id',
          as: 'track'
        }
      },
      { $unwind: '$track' },
      {
        $project: {
          title: '$track.title',
          artist: '$track.artist',
          album: '$track.album',
          genre: '$track.genre',
          duration: '$track.duration',
          plays: 1,
          totalDuration: 1,
          avgCompletionRate: { $round: ['$avgCompletionRate', 2] },
          lastPlayed: 1
        }
      },
      { $sort: { plays: -1 } },
      { $limit: parseInt(limit) }
    ]);

    res.json({
      success: true,
      data: topTracks,
      filters: {
        storeId,
        timeRange,
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error fetching store top tracks:', error);
    res.status(500).json({
      error: 'Failed to fetch store top tracks',
      details: error.message
    });
  }
};
