const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testStoresAPI() {
  try {
    // Create a test token
    const token = jwt.sign(
      { 
        userId: '676171a3a73a719f3a73a71f',
        role: 'admin',
        organization: 'SAMRO'
      },
      process.env.JWT_SECRET || 'mysecretpassword123',
      { expiresIn: '24h' }
    );
    
    console.log('Testing stores API...');
    
    // Test the stores endpoint
    try {
      const response = await axios.get('http://localhost:5000/api/stores', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Stores API Response Status:', response.status);
      console.log('Stores API Response Data:', JSON.stringify(response.data, null, 2));
      
    } catch (apiError) {
      console.error('Stores API Error:', apiError.response?.status, apiError.response?.data);
    }
    
    // Test the activities endpoint
    try {
      const response = await axios.get('http://localhost:5000/api/compliance/activities', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        params: {
          page: 1,
          limit: 5
        }
      });
      
      console.log('Activities API Response Status:', response.status);
      console.log('Activities API Response Data:', JSON.stringify(response.data, null, 2));
      
    } catch (apiError) {
      console.error('Activities API Error:', apiError.response?.status, apiError.response?.data);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testStoresAPI();
