const mongoose = require('mongoose');
const ActivityLog = require('../models/ActivityLog.model');
const Store = require('../models/Store.model');
const Track = require('../models/Track.model');
const Playlist = require('../models/Playlist.model');

async function createSampleActivities() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/traksong');
    console.log('Connected to MongoDB');

    // Get or create a sample store
    let store = await Store.findOne();
    if (!store) {
      store = new Store({
        name: 'Sample Store',
        location: 'Cape Town, South Africa',
        status: 'active'
      });
      await store.save();
      console.log('Created sample store:', store._id);
    }

    // Get or create sample tracks
    let track1 = await Track.findOne();
    if (!track1) {
      track1 = new Track({
        title: 'Sample Song 1',
        artist: 'Sample Artist 1',
        album: 'Sample Album 1',
        duration: 180,
        genre: 'Pop'
      });
      await track1.save();
    }

    let track2 = await Track.findOne({ title: { $ne: track1.title } });
    if (!track2) {
      track2 = new Track({
        title: 'Sample Song 2',
        artist: 'Sample Artist 2',
        album: 'Sample Album 2',
        duration: 210,
        genre: 'Rock'
      });
      await track2.save();
    }

    // Get or create sample playlist
    let playlist = await Playlist.findOne();
    if (!playlist) {
      playlist = new Playlist({
        name: 'Sample Playlist',
        description: 'A sample playlist for testing',
        tracks: [track1._id, track2._id],
        storeId: store._id
      });
      await playlist.save();
    }

    // Clear existing activities
    await ActivityLog.deleteMany({});
    console.log('Cleared existing activities');

    // Create sample activities
    const activities = [];
    const now = new Date();

    // Create activities for the last 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
      
      // Music player activities
      activities.push({
        storeId: store._id,
        deviceId: 'device-001',
        sessionId: `session-${i}-1`,
        activityType: 'music_player_start',
        sourceFrom: 'silence',
        sourceTo: 'music_player',
        musicPlayerData: {
          trackId: track1._id,
          playlistId: playlist._id,
          playlistName: playlist.name,
          trackTitle: track1.title,
          trackArtist: track1.artist,
          trackPosition: 1,
          isShuffled: false,
          isRepeating: false,
          volume: 75
        },
        timestamp: new Date(date.getTime() + (9 * 60 * 60 * 1000)), // 9 AM
        duration: 180,
        compliance: {
          requiresReporting: true,
          reportedToSAMRO: false,
          reportedToSAMPRA: false,
          reportedToRISA: false
        },
        auditInfo: {
          createdBy: store._id,
          verificationStatus: 'verified'
        }
      });

      // Radio activities
      activities.push({
        storeId: store._id,
        deviceId: 'device-001',
        sessionId: `session-${i}-2`,
        activityType: 'radio_start',
        sourceFrom: 'music_player',
        sourceTo: 'radio_stream',
        radioData: {
          stationName: 'Sample Radio Station',
          frequency: '94.7 FM',
          genre: 'Pop',
          country: 'South Africa',
          volume: 80
        },
        timestamp: new Date(date.getTime() + (14 * 60 * 60 * 1000)), // 2 PM
        duration: 3600,
        compliance: {
          requiresReporting: true,
          reportedToSAMRO: false,
          reportedToSAMPRA: false,
          reportedToRISA: false
        },
        auditInfo: {
          createdBy: store._id,
          verificationStatus: 'verified'
        }
      });

      // Source switch activity
      activities.push({
        storeId: store._id,
        deviceId: 'device-001',
        sessionId: `session-${i}-3`,
        activityType: 'source_switch',
        sourceFrom: 'radio_stream',
        sourceTo: 'music_player',
        musicPlayerData: {
          trackId: track2._id,
          playlistId: playlist._id,
          playlistName: playlist.name,
          trackTitle: track2.title,
          trackArtist: track2.artist,
          trackPosition: 2,
          isShuffled: false,
          isRepeating: false,
          volume: 70
        },
        timestamp: new Date(date.getTime() + (17 * 60 * 60 * 1000)), // 5 PM
        duration: 210,
        compliance: {
          requiresReporting: true,
          reportedToSAMRO: false,
          reportedToSAMPRA: false,
          reportedToRISA: false
        },
        auditInfo: {
          createdBy: store._id,
          verificationStatus: 'verified'
        }
      });
    }

    // Insert all activities
    await ActivityLog.insertMany(activities);
    console.log(`Created ${activities.length} sample activities`);

    // Verify the data
    const count = await ActivityLog.countDocuments();
    console.log(`Total activities in database: ${count}`);

    const recentActivities = await ActivityLog.find()
      .populate('storeId', 'name location')
      .sort({ timestamp: -1 })
      .limit(5);
    
    console.log('Recent activities:');
    recentActivities.forEach(activity => {
      console.log(`- ${activity.activityType} at ${activity.timestamp} (Store: ${activity.storeId?.name})`);
    });

  } catch (error) {
    console.error('Error creating sample activities:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createSampleActivities();
