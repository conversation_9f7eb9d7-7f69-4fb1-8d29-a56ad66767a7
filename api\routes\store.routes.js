const express = require('express');
const router = express.Router();
const { authenticate } = require('../middleware/auth.middleware');
const {
  getAllStores,
  getStoreById,
  getStoreAnalytics,
  getStoreTopTracks
} = require('../controllers/store.controller');

// Get all stores
router.get('/', authenticate, getAllStores);

// Store information
router.get('/:id', authenticate, getStoreById);

// Store analytics
router.get('/:storeId/analytics', authenticate, getStoreAnalytics);
router.get('/:storeId/top-tracks', authenticate, getStoreTopTracks);

module.exports = router;
