import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { reportService, historyService, adminService } from '../services/api';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Divider
} from '@mui/material';
import {
  Download as DownloadIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Assessment as AssessmentIcon,
  DateRange as DateRangeIcon,
  Store as StoreIcon,
  MusicNote as MusicNoteIcon,
  TrendingUp as TrendingUpIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon
} from '@mui/icons-material';
// Date picker imports removed for compatibility

const MaterialReports = () => {
  const { user, isAdmin } = useAuth();
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState([]);

  // Debug authentication
  console.log('MaterialReports - User:', user, 'IsAdmin:', isAdmin, 'Token:', localStorage.getItem('token'));
  const [stores, setStores] = useState([]);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days to catch seed data
    endDate: new Date(),
    storeId: '',
    track: '',
    store: ''
  });
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [exportType, setExportType] = useState('csv');
  const [error, setError] = useState('');

  useEffect(() => {
    if (isAdmin) {
      loadStores();
    }
    loadReports();
  }, [isAdmin]);

  // Test API connectivity
  useEffect(() => {
    const testAPI = async () => {
      try {
        console.log('Testing API connectivity...');
        const response = await fetch('http://localhost:5000/api/history?limit=1', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        const data = await response.json();
        console.log('Direct API test result:', data);
      } catch (error) {
        console.error('Direct API test failed:', error);
      }
    };
    testAPI();
  }, []);

  const loadStores = async () => {
    try {
      const response = await adminService.getAllStores();
      // Ensure stores is always an array
      setStores(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('Failed to load stores:', error);
      // Set empty array on error
      setStores([]);
    }
  };

  const loadReports = async () => {
    setLoading(true);
    setError('');
    try {
      const params = {
        startDate: filters.startDate.toISOString().split('T')[0],
        endDate: filters.endDate.toISOString().split('T')[0],
        limit: 50, // Add explicit limit
        ...(filters.storeId && { storeId: filters.storeId }),
        ...(filters.track && { track: filters.track }),
        ...(filters.store && { store: filters.store })
      };

      console.log('Date range:', {
        startDate: filters.startDate,
        endDate: filters.endDate,
        startDateFormatted: params.startDate,
        endDateFormatted: params.endDate
      });

      console.log('Loading reports with params:', params);
      const response = await historyService.get(params);
      console.log('Reports API response:', response);
      console.log('Response structure:', {
        data: response.data,
        dataType: typeof response.data,
        hasData: response.data?.data,
        dataLength: response.data?.data?.length,
        success: response.data?.success
      });

      // Ensure reports is always an array - API returns data in response.data.data
      const responseData = response.data?.data || response.data || [];
      console.log('Processed response data:', responseData, 'Length:', responseData.length);
      setReports(Array.isArray(responseData) ? responseData : []);

      // If no data found with date filters, try without date filters as fallback
      if (responseData.length === 0 && (params.startDate || params.endDate)) {
        console.log('No data found with date filters, trying without date filters...');
        const fallbackParams = { limit: 50 };
        const fallbackResponse = await historyService.get(fallbackParams);
        const fallbackData = fallbackResponse.data?.data || fallbackResponse.data || [];
        console.log('Fallback data:', fallbackData, 'Length:', fallbackData.length);
        setReports(Array.isArray(fallbackData) ? fallbackData : []);
      }
    } catch (error) {
      console.error('Failed to load reports:', error);
      setError('Failed to load reports');
      // Set empty array on error
      setReports([]);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString().split('T')[0],
        endDate: filters.endDate.toISOString().split('T')[0],
        ...(filters.storeId && { storeId: filters.storeId }),
        ...(filters.track && { track: filters.track }),
        ...(filters.store && { store: filters.store })
      };

      let response;
      let filename;

      if (exportType === 'csv') {
        response = await reportService.exportCSV(params);
        filename = `compliance-report-${params.startDate}-to-${params.endDate}.csv`;
      } else {
        response = await reportService.exportPDF(params);
        filename = `compliance-report-${params.startDate}-to-${params.endDate}.pdf`;
      }

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setExportDialogOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
      setError('Export failed. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString();
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate summary statistics - ensure reports is always an array
  const reportsArray = Array.isArray(reports) ? reports : [];
  const totalPlays = reportsArray.length;
  const totalDuration = reportsArray.reduce((sum, report) => sum + (report.durationPlayed || 0), 0);
  const uniqueTracks = new Set(reportsArray.map(r => r.trackId?._id)).size;
  const uniqueStores = new Set(reportsArray.map(r => r.storeId?._id)).size;

  return (
    <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              Compliance Reports
            </Typography>
            <Typography variant="body1" sx={{ color: 'text.secondary' }}>
              Music licensing and compliance reports
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<DownloadIcon />}
            onClick={() => setExportDialogOpen(true)}
            sx={{ bgcolor: 'primary.main' }}
          >
            Export Report
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      bgcolor: 'primary.main',
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <TrendingUpIcon sx={{ color: 'white' }} />
                  </Box>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 700 }}>
                      {totalPlays}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Total Plays
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      bgcolor: 'success.main',
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <MusicNoteIcon sx={{ color: 'white' }} />
                  </Box>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 700 }}>
                      {uniqueTracks}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Unique Tracks
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      bgcolor: 'warning.main',
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <StoreIcon sx={{ color: 'white' }} />
                  </Box>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 700 }}>
                      {isAdmin ? uniqueStores : 1}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {isAdmin ? 'Stores' : 'Store'}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      bgcolor: 'secondary.main',
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <DateRangeIcon sx={{ color: 'white' }} />
                  </Box>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 700 }}>
                      {formatDuration(totalDuration)}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Total Duration
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Filter Reports
          </Typography>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={filters.startDate.toISOString().split('T')[0]}
                onChange={(e) => setFilters({ ...filters, startDate: new Date(e.target.value) })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={filters.endDate.toISOString().split('T')[0]}
                onChange={(e) => setFilters({ ...filters, endDate: new Date(e.target.value) })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            {isAdmin && (
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Store</InputLabel>
                  <Select
                    value={filters.storeId}
                    label="Store"
                    onChange={(e) => setFilters({ ...filters, storeId: e.target.value })}
                  >
                    <MenuItem value="">All Stores</MenuItem>
                    {(Array.isArray(stores) ? stores : []).map(store => (
                      <MenuItem key={store._id} value={store._id}>{store.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Search Track"
                value={filters.track}
                onChange={(e) => setFilters({ ...filters, track: e.target.value })}
                placeholder="Track name..."
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={loadReports}
                sx={{ mr: 2 }}
              >
                Apply Filters
              </Button>
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                onClick={() => setFilters({
                  startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                  endDate: new Date(),
                  storeId: '',
                  track: '',
                  store: ''
                })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <Paper sx={{ p: 2, mb: 2, bgcolor: 'info.light' }}>
            <Typography variant="body2">
              Debug: Reports array length: {reportsArray.length}, Loading: {loading.toString()}, Error: {error || 'none'}
            </Typography>
            <Typography variant="body2">
              First report: {reportsArray.length > 0 ? JSON.stringify(reportsArray[0], null, 2).substring(0, 200) + '...' : 'none'}
            </Typography>
          </Paper>
        )}

        {/* Reports Table */}
        <Paper sx={{ borderRadius: 3, overflow: 'hidden' }}>
          <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Play History ({reportsArray.length} records)
            </Typography>
          </Box>

          {loading ? (
            <LinearProgress />
          ) : (
            <TableContainer sx={{ maxHeight: 600 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 600 }}>Date</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Time</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Track</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Artist</TableCell>
                    {isAdmin && <TableCell sx={{ fontWeight: 600 }}>Store</TableCell>}
                    <TableCell sx={{ fontWeight: 600 }}>Duration</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Device</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {reportsArray.map((report, index) => (
                    <TableRow key={index} sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                      <TableCell>{formatDate(report.startTime)}</TableCell>
                      <TableCell>{formatTime(report.startTime)}</TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {report.trackId?.title || 'Unknown Track'}
                        </Typography>
                      </TableCell>
                      <TableCell>{report.trackId?.artist || 'Unknown Artist'}</TableCell>
                      {isAdmin && (
                        <TableCell>
                          <Chip
                            label={report.storeId?.name || 'Unknown Store'}
                            size="small"
                            sx={{ bgcolor: 'grey.100' }}
                          />
                        </TableCell>
                      )}
                      <TableCell>{formatDuration(report.durationPlayed)}</TableCell>
                      <TableCell>{report.deviceId || '--'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>

        {/* Export Dialog */}
        <Dialog
          open={exportDialogOpen}
          onClose={() => setExportDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Export RISA Report</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
                Export play history data in RISA-compliant format for the selected date range.
              </Typography>

              <FormControl fullWidth>
                <InputLabel>Export Format</InputLabel>
                <Select
                  value={exportType}
                  label="Export Format"
                  onChange={(e) => setExportType(e.target.value)}
                >
                  <MenuItem value="csv">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CsvIcon />
                      CSV (Excel Compatible)
                    </Box>
                  </MenuItem>
                  <MenuItem value="pdf">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PdfIcon />
                      PDF Report
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                  Report Summary:
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  • Date Range: {formatDate(filters.startDate)} - {formatDate(filters.endDate)}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  • Total Records: {reportsArray.length}
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  • Unique Tracks: {uniqueTracks}
                </Typography>
                {isAdmin && (
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    • Stores: {uniqueStores}
                  </Typography>
                )}
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleExport} variant="contained" startIcon={<DownloadIcon />}>
              Export Report
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
  );
};

export default MaterialReports;
